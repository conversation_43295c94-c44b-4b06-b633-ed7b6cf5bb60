/**
 * TabBar 图标配置
 * 解决 H5 环境下 base 路径导致的图片显示问题
 */

// 方案1: 使用相对路径
export const tabBarIcons = {
  msg: {
    normal: 'static/aidex/tabbar/msg_1.png',
    selected: 'static/aidex/tabbar/msg_2.png'
  },
  workbench: {
    normal: 'static/aidex/tabbar/apply_1.png',
    selected: 'static/aidex/tabbar/apply_2.png'
  },
  book: {
    normal: 'static/aidex/tabbar/book_1.png',
    selected: 'static/aidex/tabbar/book_2.png'
  },
  user: {
    normal: 'static/aidex/tabbar/my_1.png',
    selected: 'static/aidex/tabbar/my_2.png'
  }
};

// 方案2: 动态获取正确的路径
export function getTabBarIconPath(iconPath) {
  // 在 H5 环境下，根据当前 base 路径动态调整
  // #ifdef H5
  const base = window.location.pathname.split('/')[1];
  if (base && base !== '') {
    return `/${base}/${iconPath}`;
  }
  // #endif
  
  return `/${iconPath}`;
}

// 方案3: 使用 import 方式引入图片（推荐）
// 注意：这种方式需要在组件中使用，不能直接在 pages.json 中使用
export async function getTabBarIconsAsBase64() {
  const icons = {};
  
  try {
    // 这里可以使用 import() 动态导入图片
    // 或者使用 fetch 获取图片并转换为 base64
    
    return icons;
  } catch (error) {
    console.error('获取 TabBar 图标失败:', error);
    return {};
  }
}
