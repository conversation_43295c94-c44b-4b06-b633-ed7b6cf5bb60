<template>

	<view class="wrap">
		<!--
		<view class="haotoplogo">
			<image  src="../../../static/haotop/image/logo/logo.jpg" mode="aspectFit" class="logoimg" ></image>
		</view>
		!-->
		<div v-if="loginType === 'currentPhone'">
			<u-tabs :list="list" :is-scroll="false" :current="current" @change="onClickItem"></u-tabs>
			<view v-if="current === 0">
				<view class="list">
					<template v-if="isDevelopment">
						<view class="list-call">
							<view class="iconfont icon-link" style="font-size: 22px;color:#5473e8;"></view>
							<view style="width: 100%;">
								<view class="server-selector" @click="showServerPicker">
									<text>{{ getApiUrlLabel() }}</text>
									<view class="iconfont icon-arrow-down" style="font-size: 16px;"></view>
								</view>
							</view>
						</view>
						<view class="list-call" v-if="selectedApiUrl === 'custom'">
							<!-- 当选择自定义时显示输入框 -->
							<input 
								class="u-input" 
								type="text" 
								v-model="customApiUrl"
								maxlength="100"
								placeholder="请输入服务器地址" 
								@blur="handleBlurInputUrl"
							/>
						</view>
					</template>
					<view class="list-call">
						<view class="iconfont icon-avatar" style="font-size: 22px;color:#5473e8;"></view>
						<input class="u-input" type="text" v-model="login.username" maxlength="32"
							:placeholder="t('login.placeholderAccount')" value="admin" @input="clearInput"/>
						<u-icon :size="50" v-if="showClearIcon" name="close-circle-fill" color="#c0c4cc" class="u-clear-icon" @click="clearIcon"/>
					</view>
					<view class="list-call">
						<view class="iconfont icon-key" style="font-size: 22px;color:#5473e8;"></view>
						<input class="u-input" type="text" v-model="login.password" maxlength="32"
							:placeholder="t('login.placeholderPassword')" :password="!showPassword" value="admin123" />
						<image class="u-icon-right"
							:src="'/static/aidex/login/eye_' + (showPassword ? 'open' : 'close') + '.png'"
							@click="showPass()"></image>
					</view>
					<div style="padding:15rpx 0 0;">
						<view class="register">
							<navigator class="register-link" url="forget" open-type="navigate">{{t('login.forget')}}
							</navigator>
							<!-- <navigator class="register-link" url="reg" open-type="navigate">{{t('login.reg')}}</navigator> -->
						</view>
						<u-checkbox-group v-model="rememberGroup">
							<u-checkbox name="remember" active-color="#5473e8">{{t('login.autoLogin')}}</u-checkbox>
						</u-checkbox-group>
					</div>
				</view>
				<button class="button" :disabled="login.loading" @click="submit('1')" :loading="login.loading"><text>登录</text></button>
				<view class="login-bottom-box">
					<u-divider> 更多登录方式 </u-divider>
					<view class="oauth2">
						<u-icon class="u-icon" size="100" color="#36c956" name="weixin-circle-fill"
							@click="wxLogin"></u-icon>
						<u-icon class="u-icon" size="100" color="#23a0f0" name="qq-circle-fill"
							@click="qqLogin"></u-icon>
					</view>
					<view class="copyright">
						登录即代表您已阅读并同意<u-link href="#">用户协议</u-link> 与 <u-link href="#">隐私政策</u-link>
					</view>
				</view>
			</view>
			<view v-if="current === 1">
				<view class="list">
					<view class="list-call">
						<view class="iconfont icon-shouji" style="font-size: 22px;color:#5473e8;"></view>
						<u-input v-model="phoneNo" placeholder="请填写手机号" style="width: 100%;"
							:border="false" :maxlength="11" prefix-icon="phone" prefix="+86">
						</u-input>
					</view>
					<div style="padding:25rpx 0 0;">
						还没有账号？<navigator class="reg-link" url="reg" open-type="navigate">{{t('login.reg')}}</navigator>
					</div>

				</view>
				<view class="button" @click="nextStep()"><text>下一步</text></view>
				<view class="login-bottom-box">
					<u-divider> 更多登录方式 </u-divider>
					<view class="oauth2">
						<u-icon class="u-icon" size="100" color="#36c956" name="weixin-circle-fill"
							@click="wxLogin"></u-icon>
						<u-icon class="u-icon" size="100" color="#23a0f0" name="qq-circle-fill"
							@click="qqLogin"></u-icon>
					</view>
					<view class="copyright">
						登录即代表您已阅读并同意<u-link href="#">用户协议</u-link> 与 <u-link href="#">隐私政策</u-link>
					</view>
				</view>

			</view>
		</div>

		<div v-if="loginType !== 'currentPhone'">
			<view class="currentPhone-box">
				<view class="number-text">183****1005</view>
				<view class="other-text">认证服务由中国移动提供。</view>
				<u-button type="primary" @click="submit('3')">本机号码一键登录</u-button>
				<u-button @click="qiehuanLogin()">其他登录方式</u-button>
			</view>
			<view class="login-bottom-box">
				<view class="copyright">
					登录即代表您已阅读并同意<u-link href="#">用户协议</u-link> 与 <u-link href="#">隐私政策</u-link>
				</view>
			</view>
		</div>
		<u-picker
			:show="showApiUrlPicker"
			mode="selector"
			:range="apiUrlList"
			range-key="label"
			@confirm="onApiUrlConfirm"
			@cancel="showApiUrlPicker = false"
		></u-picker>
	</view>
</template>
<script setup>
	/**
	 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
	 */
	import { ref, reactive, computed, onMounted, watch } from 'vue'
	import { useI18n } from 'vue-i18n'
	import { useStore } from 'vuex'
	import config from '@/common/config.js';
	import base64 from '@/common/base64.js';
	import util from '@/common/util.js';

	// 使用 i18n
	const { t } = useI18n()

	// 使用 Vuex store
	const store = useStore()

	// 响应式数据
	const isDevelopment = ref(config.isDevelopment)
	const showClearIcon = ref(false)
	const login = reactive({
		loading: false,
		username: "",
		password: ""
	})

	const showApiUrlPicker = ref(false)
	const selectedApiUrl = ref(config.isDevelopment ? config.devApiUrlOptions[0].value : config.productionApiUrl)
	const customApiUrl = ref('http://************:50001/hcscm/pda/v1/')
	const apiUrlList = ref(config.isDevelopment ? config.devApiUrlOptions : [])
	const phoneNo = ref('')
	const loginType = ref('currentPhone')
	const showPassword = ref(false)
	const remember = ref(true)
const rememberGroup = ref(['remember'])
	const isValidCodeLogin = ref(false)
	const validCode = ref('')
	const imgValidCodeSrc = ref(null)
	const list = ref([{
		name: '用户名'
	}, {
		name: '手机号'
	}])
	const current = ref(0)
	const activeColor = ref('#007aff')

	// 监听rememberGroup变化，同步remember状态
	watch(rememberGroup, (newVal) => {
		remember.value = newVal.includes('remember')
	}, { immediate: true })

	// 生命周期钩子
	onMounted(() => {
		console.log('🎉 登录页面已加载！');
		console.log('📱 当前页面路径:', 'pages/sys/login/index');
		console.log('🔧 Vue 版本: Vue 3');
		console.log('📦 uView Plus 是否可用:', !!uni.$u);
	})

	// onShow 等价函数
	const handleShow = () => {
		console.log('👀 登录页面显示中...');

		// 只在开发环境读取存储的URL
		if (config.isDevelopment) {
			uni.getStorage({
				key: 'ApiUrl',
				success: (e) => {
					util.updateApiUrl(e.data);
					selectedApiUrl.value = e.data;
				}
			});
		}
		uni.getStorage({
			key: 'UserInfo',
			success: (e) => {
				console.log("---->>>" + e.data.username + '--->>' + e.data.password);
				login.username = e.data.username;
				login.password = e.data.password;
			}
		})
	}

	// 方法定义
	const clearInput = (event) => {
		if (event.detail.value.length > 0) {
			showClearIcon.value = true;
		} else {
			showClearIcon.value = false;
		}
	}

	const clearIcon = () => {
		login.username = ''
		showClearIcon.value = false;
	}

	const showServerPicker = () => {
		showApiUrlPicker.value = true;
	}

	const getApiUrlLabel = () => {
		if (selectedApiUrl.value === 'custom') {
			return '自定义服务器';
		}
		const option = apiUrlList.value.find(item => item.value === selectedApiUrl.value);
		return option ? option.label : '选择服务器';
	}

	const onApiUrlConfirm = (e) => {
		console.log('e',JSON.stringify(e));
		const selectedValue = apiUrlList.value[e[0]].value;
		selectedApiUrl.value = selectedValue;
		showApiUrlPicker.value = false;

		// 如果不是自定义，直接更新 store
		if (selectedValue !== 'custom') {
			store.dispatch('updateApiUrl', selectedValue);
		}
		// 如果是自定义且已有自定义值，更新store
		else if (customApiUrl.value) {
			store.dispatch('updateApiUrl', customApiUrl.value);
		}
	}

	const showPass = () => {
		showPassword.value = !showPassword.value;
	}

	const qiehuanLogin = () => {
		loginType.value = 'other'
	}

	const clickchecked = () => {
		remember.value = !remember.value
		if (remember.value == true) {
			if ((login.username.length > 0) && (login.password.length > 0)) {
				uni.setStorage({
					key: 'UserInfo',
					data: login
				})
			}
		}
	}

	const onClickItem = (index) => {
		current.value = index;
	}
			refreshImgValidCode(e) {
				if (this.vuex_token == '') {
					this.$u.api.index().then(res => {
						this.imgValidCodeSrc = this.vuex_config.baseUrl + '/validCode?__sid=' +
							res.sessionid + '&t=' + new Date().getTime();
					});
				} else {
					this.imgValidCodeSrc = this.vuex_config.baseUrl + '/validCode?__sid=' +
						this.vuex_token + '&t=' + new Date().getTime();
				}
				this.validCode = '';
			},
			nextStep() {
				//验证码登录下一步
				uni.showLoading({
					title: '正在获取验证码',
					mask: true
				})
				this.$u.api.sendCode({
						phoneNo: this.phoneNo,
						validCodeType: '2'
					})
					.then(res => {
						if (res.code == '200') {
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/sys/login/code?phoneNo=' + this.phoneNo
								});
							}, 500);
						} else {
							this.$u.toast(res.msg);
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/sys/login/code?phoneNo=' + this.phoneNo
								});
							}, 500);
						}
					});
			},
			submit(loginType = '1') {
				uni.showLoading({
					title: '登录中...'
				});
				
				let currentApiUrl;
				
				if (config.isDevelopment) {
					// 开发环境：使用选择的URL
					currentApiUrl = this.selectedApiUrl === 'custom' ? this.customApiUrl : this.selectedApiUrl;
					if (!currentApiUrl || currentApiUrl.length === 0) {
						this.$u.toast('请输入服务器地址');
						return;
					}
					// 更新store中的URL
					this.$store.dispatch('updateApiUrl', currentApiUrl);
				} else {
					// 生产环境：使用固定的生产URL
					currentApiUrl = config.productionApiUrl;
				}
				
				if (this.login.username.length == 0) {
					this.$u.toast('请输入账号');
					return;
				}
				if (this.login.password.length == 0) {
					this.$u.toast('请输入密码');
					return;
				}
				console.log('开始请求', currentApiUrl + 'login')
				this.login.loading = true
				
				// 临时设置baseURL为当前选择的API URL - 修正：使用回调函数
				const originalBaseUrl = this.$u.http.config.baseURL || this.$u.http.config.baseUrl;
				this.$u.http.setConfig((config) => {
					config.baseURL = currentApiUrl;
					return config;
				});
				
				this.$u.api.pdaLogin({
					'phone': this.login.username,
					'password': this.login.password,
				}).then((res) => {
					console.log('login res',res)
					var aLoginUserData = res;
					
					//console.log('--aLoginUserData->>' + JSON.stringify(aLoginUserData));
					// 只在开发环境保存 apiurl 到本地存储
					if (config.isDevelopment) {
						uni.setStorage({
							key: 'ApiUrl',
							data: currentApiUrl
						});
						util.updateApiUrl(currentApiUrl);
					}
					// 保存 token 到本地存储
					uni.setStorageSync('RemoteTokenData', aLoginUserData);

					uni.setStorage({
						key:'userToken',
						data: {
							Token: aLoginUserData.token,
						} 
					});
					
					getApp().globalData.Token = aLoginUserData.token;
					getApp().globalData.EmployeeID = aLoginUserData.user_id;
					getApp().globalData.IsSaleUserStatus = 0;
					getApp().globalData.LoginID = aLoginUserData.user_id;
					getApp().globalData.LoginName = aLoginUserData.user_name;
					getApp().globalData.PlanDepartmentID = aLoginUserData.default_sale_system_id;
					getApp().globalData.PlanDepartmentName = aLoginUserData.default_sale_system_name;
					getApp().globalData.UserGroup = '';
					getApp().globalData.StoreNameID = aLoginUserData.default_physical_warehouse_id;
					getApp().globalData.StoreName = aLoginUserData.default_physical_warehouse_name;
					getApp().globalData.StoreTypeNo = '';
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/sys/workbench/index'
						});
					}, 500);
					
					var that = this
					if (that.remember == true) {
						if ((this.login.username.length > 0) && (this.login.password.length > 0)) {
							uni.setStorage({
								key: 'UserInfo',
								data: that.login
							})
						}

					}

					uni.showToast({
						icon: 'none',
						title: '登录成功!'
					});
					this.$store.dispatch('initNotification');
					setTimeout(function() {
						uni.hideLoading();
					}, 1000);
				}).catch((error) => {
					console.log('login error', error)
					uni.showToast({
						icon: 'none',
						title: '连接服务器出错，请检查后台服务是否启动！' + (error.msg || error.message || '')
					});
					setTimeout(function() {
						uni.hideLoading();
					}, 5000);
					// 恢复原始baseURL - 修正：使用回调函数
					this.$u.http.setConfig((config) => {
						config.baseURL = originalBaseUrl;
						return config;
					});
				}).finally(() => {
					this.login.loading = false;
				})
			},
			wxLogin(res) {
				this.$u.toast('微信登录');
			},
			qqLogin() {
				this.$u.toast('QQ 登录');
			},
			handleBlurInputUrl(e) {
				// 只在开发环境处理自定义URL
				if (config.isDevelopment) {
					const url = e.detail.value.trim();
					if (url) {
						this.customApiUrl = url;
						// 保存到本地存储
						uni.setStorage({
							key: 'ApiUrl',
							data: url
						});
						util.updateApiUrl(url);
					}
				}
			},
		}
	};
</script>
<style lang="scss">
	@import 'index.scss';
	.server-selector {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10rpx 0;
        border-bottom: 1px solid #eee;
    }
.list-call {
    .u-select {
        margin-bottom: 10rpx;
    }
    .u-input {
        margin-top: 10rpx;
    }
}
	.haotoplogo {
		width: 450rpx;
		height: 450rpx;
		max-width: 5%;
		max-height: 5%;
		margin: 10rpx 10% 10rpx 8%;
	}

	.logo {
		width: 80%;
		font-size: 64rpx;
		color: #5473e8;
		margin: 80rpx auto 80rpx auto;
	}

	.list-call-icon {
		color: #ff0000;
	}

	.currentPhone-box {
		text-align: center;
		padding: 40rpx 80rpx;

		.number-text {
			color: #000000;
			font-size: 60rpx;
		}

		.other-text {
			color: #999999;
			font-size: 26rpx;
			padding: 20rpx 0;
		}

		.u-btn {
			margin: 30rpx auto;
		}

		.u-hairline-border {
			border: 1px solid #fff;
		}
	}

	.register {
		display: inline-block;
		color: #5473e8;
		height: 40rpx;
		line-height: 40rpx;
		font-size: 28rpx;
		float: right;
		margin-top: 6rpx;
	}

	.register-link {
		float: right;
		padding: 0 16rpx;
	}

	.reg-link {
		display: inline-block;
		color: #5473e8;
	}

	.oauth2 {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		margin: 0rpx 100rpx 30rpx;

		image {
			height: 80rpx;
			width: 80rpx;
		}
	}

	.u-tabs {
		padding: 0 70rpx;
	}
</style>
