/*!
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 * <AUTHOR>
 * @version 2020-9-1
 */
.wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.pages-sys-login-index .wrap {
  background: url(../../../static/aidex/login-bg.png) no-repeat left top;
  background-size: 100% auto;
}

.list {
  display: flex;
  flex-direction: column;
  padding: 40rpx 70rpx 40rpx 70rpx;
}

.list-call {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-top: 10rpx;
  height: 120rpx;
  font-weight: normal;
  color: #333333;
  border-bottom: 0.5px solid #e2e2e2;
}

.list-call .up-input {
  flex: 1;
  font-size: 39rpx;
  text-align: left;
  margin-left: 16rpx;
}

.list-call .u-icon-right {
  color: #aaaaaa;
  width: 50rpx;
  height: 40rpx;
}

.button {
  color: #ffffff;
  font-size: 32rpx;
  width: 80%;
  height: 80rpx;
  background: #497bff;
  box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(15, 168, 250, 0.4);
  border-radius: 10rpx;
  line-height: 80rpx;
  text-align: center;
  margin: 50rpx auto 0;
}

.img-valid-code img {
  width: 30rpx;
  heigth: 50rpx;
}

.btn-valid-code {
  color: #da7918;
  font-size: 30rpx;
  line-height: 48rpx;
  padding: 6rpx 35rpx;
  border: 1rpx solid #da7918;
  border-radius: 50rpx;
}

.btn-valid-code-hover {
  background-color: #f3f3f3;
}

.btn-valid-codes {
  color: #999999 !important;
  border: 1rpx solid #999999;
}
.login-bottom-box {
  position: fixed;
  bottom: 40rpx;
  text-align: center;
  width: 100%;
}
.copyright {
  text-align: center;
  color: #939393;
  width: 100%;
  font-size: 24rpx;
  .u-link {
    margin: 0 10rpx;
    font-size: 24rpx !important;
  }
}

.pages-sys-login-index,
.pages-sys-login-reg,
.pages-sys-login-forget {
  .up-checkbox__label {
    font-size: 28rpx !important;
  }
  .up-label {
    flex: 0 0 35px !important;
    width: 35px !important;
  }
}
.reg-text {
  font-size: 42rpx;
  color: #000;
  padding: 40rpx 70rpx 10rpx;
}
