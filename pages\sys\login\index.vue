<template>
	<view class="wrap">
		<div v-if="loginType === 'currentPhone'">
			<up-tabs :list="list" :is-scroll="false" :current="current" @change="onClickItem"></up-tabs>
			<view v-if="current === 0">
				<view class="list">
					<template v-if="isDevelopment">
						<view class="list-call">
							<view class="iconfont icon-link" style="font-size: 22px;color:#5473e8;"></view>
							<view style="width: 100%;">
								<view class="server-selector" @click="showServerPicker">
									<text>{{ getApiUrlLabel() }}</text>
									<view class="iconfont icon-arrow-down" style="font-size: 16px;"></view>
								</view>
							</view>
						</view>
						<view class="list-call" v-if="selectedApiUrl === 'custom'">
							<input 
								class="up-input" 
								type="text" 
								v-model="customApiUrl"
								maxlength="100"
								placeholder="请输入服务器地址" 
								@blur="handleBlurInputUrl"
							/>
						</view>
					</template>
					<view class="list-call">
						<view class="iconfont icon-avatar" style="font-size: 22px;color:#5473e8;"></view>
						<input class="up-input" type="text" v-model="login.username" maxlength="32"
							:placeholder="t('login.placeholderAccount')" value="" @input="clearInput"/>
						<up-icon :size="30" v-if="showClearIcon" name="close-circle-fill" color="#c0c4cc" class="up-clear-icon" @click="clearIcon"/>
					</view>
					<view class="list-call">
						<view class="iconfont icon-key" style="font-size: 22px;color:#5473e8;"></view>
						<input class="up-input" type="text" v-model="login.password" maxlength="32"
							:placeholder="t('login.placeholderPassword')" :password="!showPassword" value="" />
						<image class="u-icon-right"
							:src="'/static/aidex/login/eye_' + (showPassword ? 'open' : 'close') + '.png'"
							@click="showPass"></image>
					</view>
					<div style="padding:15rpx 0 0; display: flex; justify-content: space-between">
						<view class="register">
							<navigator class="register-link" url="forget" open-type="navigate">{{t('login.forget')}}
							</navigator>
						</view>
						<up-checkbox-group v-model="rememberGroup">
							<up-checkbox name="remember" active-color="#5473e8" :label="t('login.autoLogin')"></up-checkbox>
						</up-checkbox-group>
					</div>
				</view>
				<button class="button" :disabled="login.loading" @click="submit('1')" :loading="login.loading"><text>登录</text></button>
			</view>
			<view v-if="current === 1">
				<view class="list">
					<view class="list-call">
						<view class="iconfont icon-shouji" style="font-size: 22px;color:#5473e8;"></view>
						<up-input v-model="phoneNo" placeholder="请填写手机号" style="width: 100%;"
							:border="false" :maxlength="11" prefix-icon="phone" prefix="+86">
						</up-input>
					</view>
					<div style="padding:25rpx 0 0;">
						还没有账号？<navigator class="reg-link" url="reg" open-type="navigate">{{t('login.reg')}}</navigator>
					</div>
				</view>
				<view class="button" @click="nextStep()"><text>下一步</text></view>
			</view>
		</div>

		<div v-if="loginType !== 'currentPhone'">
			<view class="currentPhone-box">
				<view class="number-text">183****1005</view>
				<view class="other-text">认证服务由中国移动提供。</view>
				<up-button type="primary" @click="submit('3')">本机号码一键登录</up-button>
				<up-button @click="qiehuanLogin()">其他登录方式</up-button>
			</view>
		</div>
		
		<up-picker
			:closeOnClickOverlay="true"
			v-model:show="showApiUrlPicker"
			:columns="apiUrlList"
			keyName="label"
			valueName="value"
			@confirm="onApiUrlConfirm"
			@cancel="showApiUrlPicker = false"
		></up-picker>
	</view>
</template>

<script setup>
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import config from '@/common/config.js';
import base64 from '@/common/base64.js';
import util from '@/common/util.js';

// 使用 i18n
const { t } = useI18n()

// 使用 Vuex store
const store = useStore()

// 响应式数据
const isDevelopment = ref(config.isDevelopment)
const showClearIcon = ref(false)
const login = reactive({
	loading: false,
	username: "",
	password: ""
})

const showApiUrlPicker = ref(false)
const selectedApiUrl = ref(config.isDevelopment ? config.devApiUrlOptions[0].value : config.productionApiUrl)
const customApiUrl = ref('http://************:50001/hcscm/pda/v1/')
const apiUrlList = ref([config.isDevelopment ? config.devApiUrlOptions : []])
const phoneNo = ref('')
const loginType = ref('currentPhone')
const showPassword = ref(false)
const remember = ref(true)
const rememberGroup = ref(['remember'])
const isValidCodeLogin = ref(false)
const validCode = ref('')
const imgValidCodeSrc = ref(null)
const list = ref([{
	name: '用户名'
}, {
	name: '手机号'
}])
const current = ref(0)
const activeColor = ref('#007aff')

// 监听rememberGroup变化，同步remember状态
watch(rememberGroup, (newVal) => {
	remember.value = newVal.includes('remember')
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
	console.log('🎉 登录页面已加载！');
	console.log('📱 当前页面路径:', 'pages/sys/login/index');
	console.log('🔧 Vue 版本: Vue 3');
	console.log('📦 uView Plus 是否可用:', !!uni.$u);
	
	// onShow 逻辑
	handleShow()
})

// onShow 等价函数
const handleShow = () => {
	console.log('👀 登录页面显示中...');

	// 只在开发环境读取存储的URL
	if (config.isDevelopment) {
		uni.getStorage({
			key: 'ApiUrl',
			success: (e) => {
				util.updateApiUrl(e.data);
				selectedApiUrl.value = e.data;
			}
		});
	}
	uni.getStorage({
		key: 'UserInfo',
		success: (e) => {
			console.log("---->>>" + e.data.username + '--->>' + e.data.password);
			login.username = e.data.username;
			login.password = e.data.password;
		}
	})
}

// 方法定义
const clearInput = (event) => {
	if (event.detail.value.length > 0) {
		showClearIcon.value = true;
	} else {
		showClearIcon.value = false;
	}
}

const clearIcon = () => {
	login.username = ''
	showClearIcon.value = false;
}

const showServerPicker = () => {
	showApiUrlPicker.value = true;
}

const getApiUrlLabel = () => {
	if (selectedApiUrl.value === 'custom') {
		return '自定义服务器';
	}
	const option = apiUrlList.value[0].find(item => item.value === selectedApiUrl.value);
	return option ? option.label : '选择服务器';
}

const onApiUrlConfirm = (e) => {
	console.log('e',JSON.stringify(e));
	const selectedValue = apiUrlList.value[0][e[0]].value;
	selectedApiUrl.value = selectedValue;
	showApiUrlPicker.value = false;
	
	// 如果不是自定义，直接更新 store
	if (selectedValue !== 'custom') {
		store.dispatch('updateApiUrl', selectedValue);
	}
	// 如果是自定义且已有自定义值，更新store
	else if (customApiUrl.value) {
		store.dispatch('updateApiUrl', customApiUrl.value);
	}
}

const showPass = () => {
	showPassword.value = !showPassword.value;
}

const qiehuanLogin = () => {
	loginType.value = 'other'
}

const clickchecked = () => {
	remember.value = !remember.value
	if (remember.value == true) {
		if ((login.username.length > 0) && (login.password.length > 0)) {
			uni.setStorage({
				key: 'UserInfo',
				data: login
			})
		}
	}
}

const onClickItem = (index) => {
	current.value = index;
}

const nextStep = () => {
	//验证码登录下一步
	uni.showLoading({
		title: '正在获取验证码',
		mask: true
	})
	uni.$u.api.sendCode({
			phoneNo: phoneNo.value,
			validCodeType: '2'
		})
		.then(res => {
			if (res.code == '200') {
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/sys/login/code?phoneNo=' + phoneNo.value
					});
				}, 500);
			} else {
				uni.$u.toast(res.msg);
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/sys/login/code?phoneNo=' + phoneNo.value
					});
				}, 500);
			}
		});
}

const submit = (loginType = '1') => {
	uni.showLoading({
		title: '登录中...'
	});
	
	let currentApiUrl;
	
	if (config.isDevelopment) {
		// 开发环境：使用选择的URL
		currentApiUrl = selectedApiUrl.value === 'custom' ? customApiUrl.value : selectedApiUrl.value;
		if (!currentApiUrl || currentApiUrl.length === 0) {
			uni.$u.toast('请输入服务器地址');
			return;
		}
		// 更新store中的URL
		store.dispatch('updateApiUrl', currentApiUrl);
	} else {
		// 生产环境：使用固定的生产URL
		currentApiUrl = config.productionApiUrl;
	}
	
	if (login.username.length == 0) {
		uni.$u.toast('请输入账号');
		return;
	}
	if (login.password.length == 0) {
		uni.$u.toast('请输入密码');
		return;
	}
	console.log('开始请求', currentApiUrl + 'login')
	login.loading = true
	
	// 临时设置baseUrl为当前选择的API URL
	const originalBaseUrl = uni.$u.http.config.baseUrl;
	uni.$u.http.setConfig({ baseUrl: currentApiUrl });
	
	uni.$u.api.pdaLogin({
		'phone': login.username,
		'password': login.password,
	}).then((res) => {
		console.log('login res',res)
		var aLoginUserData = res;
		
		// 只在开发环境保存 apiurl 到本地存储
		if (config.isDevelopment) {
			uni.setStorage({
				key: 'ApiUrl',
				data: currentApiUrl
			});
			util.updateApiUrl(currentApiUrl);
		}
		// 保存 token 到本地存储
		uni.setStorageSync('RemoteTokenData', aLoginUserData);

		uni.setStorage({
			key:'userToken',
			data: {
				Token: aLoginUserData.token,
			} 
		});
		
		getApp().globalData.Token = aLoginUserData.token;
		getApp().globalData.EmployeeID = aLoginUserData.user_id;
		getApp().globalData.IsSaleUserStatus = 0;
		getApp().globalData.LoginID = aLoginUserData.user_id;
		getApp().globalData.LoginName = aLoginUserData.user_name;
		getApp().globalData.PlanDepartmentID = aLoginUserData.default_sale_system_id;
		getApp().globalData.PlanDepartmentName = aLoginUserData.default_sale_system_name;
		getApp().globalData.UserGroup = '';
		getApp().globalData.StoreNameID = aLoginUserData.default_physical_warehouse_id;
		getApp().globalData.StoreName = aLoginUserData.default_physical_warehouse_name;
		getApp().globalData.StoreTypeNo = '';
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/sys/workbench/index'
			});
		}, 500);
		
		if (remember.value == true) {
			if ((login.username.length > 0) && (login.password.length > 0)) {
				uni.setStorage({
					key: 'UserInfo',
					data: login
				})
			}
		}

		uni.showToast({
			icon: 'none',
			title: '登录成功!'
		});
		store.dispatch('initNotification');
		setTimeout(function() {
			uni.hideLoading();
		}, 1000);
	}).catch((error) => {
		console.log('login error', error)
		uni.showToast({
			icon: 'none',
			title: '连接服务器出错，请检查后台服务是否启动！' + (error.msg || error.message || '')
		});
		setTimeout(function() {
			uni.hideLoading();
		}, 5000);
		// 恢复原始baseUrl
		uni.$u.http.setConfig({ baseUrl: originalBaseUrl });
	}).finally(() => {
		login.loading = false;
	})
}

const wxLogin = (res) => {
	uni.$u.toast('微信登录');
}

const qqLogin = () => {
	uni.$u.toast('QQ 登录');
}

const handleBlurInputUrl = (e) => {
	// 只在开发环境处理自定义URL
	if (config.isDevelopment) {
		const url = e.detail.value.trim();
		if (url) {
			customApiUrl.value = url;
			// 保存到本地存储
			uni.setStorage({
				key: 'ApiUrl',
				data: url
			});
			util.updateApiUrl(url);
		}
	}
}
</script>

<style lang="scss">
@import 'index.scss';
.server-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 0;
    border-bottom: 1px solid #eee;
}
.list-call {
    .up-select {
        margin-bottom: 10rpx;
    }
    .up-input {
        margin-top: 10rpx;
    }
}
</style>
